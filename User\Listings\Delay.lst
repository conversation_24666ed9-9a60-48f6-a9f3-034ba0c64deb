C251 COMPILER V5.60.0,  Delay                                                              27/07/25  18:48:49  PAGE 1   


C251 COMPILER V5.60.0, COMPILATION OF MODULE Delay
OBJECT MODULE PLACED IN .\Objects\Delay.obj
COMPILER INVOKED BY: D:\Keil_v5\C251\BIN\C251.EXE ..\Driver\Delay.c XSMALL INTR2 WARNINGLEVEL(1) BROWSE INCDIR(..\App;..
                    -\Driver;..\User;..\App\znFAT) DEBUG PRINT(.\Listings\Delay.lst) TABS(2) OBJECT(.\Objects\Delay.obj) 

stmt  level    source

    1          #include "Delay.h"
    2          
    3          void Delay_X_mS(unsigned int ms)
    4          {
    5   1        unsigned int i;
    6   1        do{
    7   2          i = MAIN_Fosc/6030;
    8   2          while(--i);
    9   2        }while(--ms);
   10   1      }
   11          void Delay_X_uS(unsigned int us)
   12          {
   13   1        unsigned int i;
   14   1        do{
   15   2          i = Main_Fosc_KHZ/6030;
   16   2          while(--i);
   17   2        }while(--us);
   18   1      }


Module Information          Static   Overlayable
------------------------------------------------
  code size            =        46     ------
  ecode size           =    ------     ------
  data size            =    ------     ------
  idata size           =    ------     ------
  pdata size           =    ------     ------
  xdata size           =    ------     ------
  xdata-const size     =    ------     ------
  edata size           =    ------     ------
  bit size             =    ------     ------
  ebit size            =    ------     ------
  bitaddressable size  =    ------     ------
  ebitaddressable size =    ------     ------
  far data size        =    ------     ------
  huge data size       =    ------     ------
  const size           =    ------     ------
  hconst size          =    ------     ------
End of Module Information.


C251 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
