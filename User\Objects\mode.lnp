".\Objects\main.obj",
".\Objects\isr.obj",
".\Objects\config.obj",
"..\Driver\stc_usb_cdc_32g.lib",
".\Objects\MPU6050.obj",
".\Objects\OLED_SPI.obj",
".\Objects\MKS.obj",
".\Objects\D2Car.obj",
".\Objects\ADC.obj",
".\Objects\Delay.obj",
".\Objects\GPIO.obj",
".\Objects\IIC.obj",
".\Objects\INT.obj",
".\Objects\PIT.obj",
".\Objects\PWM.obj",
".\Objects\UART.obj",
".\Objects\EEPROM.obj",
".\Objects\CAN.obj",
".\Objects\DMA.obj",
".\Objects\SPI.obj" 
TO ".\Objects\mode" 
PRINT(".\Listings\mode.map") CASE DISABLEWARNING (57) 
CLASSES (EDATA (0x0-0xFFF), 
HDATA (0x0-0xFFF)) 
