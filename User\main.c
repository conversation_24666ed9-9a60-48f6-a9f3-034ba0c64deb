/*****************************************************************************
* 例程名称		 : 编码器控制舵机
* 说明         : 通过EC11编码器的旋转控制舵机运动，编码器正转舵机正转，编码器反转舵机反转
* 接线方式     : EC11编码器A相连接P32(INT1)，B相连接P34，按键连接P33，舵机PWM信号连接P01
* 注意    		 : 舵机控制脉宽范围500-2500us，中位1500us，按下按键复位到中位
*******************************************************************************/

//--包含你要使用的头文件--//
#include "config.h"          //通用配置头文件

/*************  本地常量声明    **************/

/*************  IO口定义    **************/


/*************  本地变量声明    **************/
bit EC11_R_flag = 0;         //EC11旋转标志
bit EC11_DIR = 0;            //EC11旋转方向
uint duty = 700;             //PWM脉宽值
bit servo_select = 0;        //舵机选择标志：0=舵机1(P00)，1=舵机2(P01)
bit key_pressed = 0;         //按键按下标志
bit key_last_state = 1;      //按键上次状态

/*************  本地函数声明    **************/
void EC11_app(void);                 //EC11编码器应用处理
uchar KEY_dat[2]={0xff,0xff};   //0=当前，1=上一时刻
/****************  外部函数声明和外部变量声明 *****************/
	
/*******************************************************************************
* 函 数 名         : main
* 函数功能		     : 主函数
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/	
void main(void)
{
	System_init();  //系统初始化	
	PIT_init_ms(0,1);		//用户任务定时器1ms

	PWM_init(PWMB_CH1_P00,50,750); //初始化PWM，50Hz频率，中位脉宽，使用P00引脚
	PWM_init(PWMB_CH2_P01,50,750); //初始化PWM，50Hz频率，中位脉宽，使用P01引脚
	INT_init(1,1); //初始化外部中断1，下降沿触发，用于EC11编码器

	EUSB = 1;   //IE2相关的中断使能后，需要重新设置EUSB
	EA = 1;  						//允许所有中断

	while(1)
	{

		EC11_app(); //EC11编码器应用处理
	}
}

void EC11_app(void)
{
	if(KEY_dat[0]==0 && KEY_dat[1]!=0) //按键按下
	{
		
	}
	KEY_dat[1] = KEY_dat[0];
	
	if(EC11_R_flag)  //编码器旋转
	{
		if(EC11_DIR)  //正转
		{
			duty += 20; //增加脉宽，舵机正转
		}
		else //反转
		{
			duty -= 20; //减少脉宽，舵机反转
		}

		//根据舵机选择标志控制对应的舵机
		if(servo_select == 0)
		{
			PWM_change(PWMB_CH1_P00, duty); //控制舵机1(P00)
		}
		else
		{
			PWM_change(PWMB_CH2_P01, duty); //控制舵机2(P01)
		}
		EC11_R_flag=0;
	}
}

/*******************************************************************************
* 函 数 名         : INT1_isr
* 函数功能		     : 外部中断1服务程序，EC11编码器旋转中断
* 输    入         : 无
* 输    出         : 无
*******************************************************************************/
void INT1_isr(void)  //EC11发生旋转
{
	EC11_R_flag=1;
	EC11_DIR = !P34;  //记录旋转方向
}

void TM0_isr(void)  //1ms用户任务定时器中断
{
	if(++T0_cnt==60000)	T0_cnt=0; //形成循环变量
	
	if(T0_cnt%8==0)	//按键窗口采集，先进先出
	{
		KEY_dat[0]<<=1;
		KEY_dat[0] |= (uchar)P32;
	}
}