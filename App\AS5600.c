/********************************************
*********************************************************/
#include "AS5600.h"
#include "config.h"

/**********  �Ĵ�����ַ   *************/
#define	AS5600_SlaveAddress   (0x36<<1)	  //����������IIC�����еĴӵ�ַ

/*------------------------------------------------------------------*/

u8 AS5600_BUF[2];                         //�������ݻ����� 

/**************************************
��ʱ5΢��(STC90C52RC@12M)
��ͬ�Ĺ�������,��Ҫ�����˺�����ע��ʱ�ӹ���ʱ��Ҫ�޸�
������1T��MCUʱ,���������ʱ����
**************************************/
void AS5600_Delay5us()
{
    unsigned long edata i;

	_nop_();
	_nop_();
	_nop_();
	i = 28UL;
	while (i) i--;
}

/**************************************
��ʱ5����(STC90C52RC@12M)
��ͬ�Ĺ�������,��Ҫ�����˺���
������1T��MCUʱ,���������ʱ����
**************************************/
void AS5600_Delay5ms()
{
    unsigned long edata i;

	_nop_();
	_nop_();
	_nop_();
	i = 29998UL;
	while (i) i--;
}

/**************************************
��ʼ�ź�
**************************************/
void AS5600_Start()
{
    AS5600_SDA = 1;                    //����������
    AS5600_SCL = 1;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ
    AS5600_SDA = 0;                    //�����½���
    AS5600_Delay5us();                 //��ʱ
    AS5600_SCL = 0;                    //����ʱ����
}

/**************************************
ֹͣ�ź�
**************************************/
void AS5600_Stop()
{
    AS5600_SDA = 0;                    //����������
    AS5600_SCL = 1;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ
    AS5600_SDA = 1;                    //����������
    AS5600_Delay5us();                 //��ʱ
}

/**************************************
����Ӧ���ź�
��ڲ���:ack (0:ACK 1:NAK)
**************************************/
void AS5600_SendACK(bit ack)
{
    AS5600_SDA = ack;                  //дӦ���ź�
    AS5600_SCL = 1;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ
    AS5600_SCL = 0;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ
}

/**************************************
����Ӧ���ź�
**************************************/
bit AS5600_RecvACK()
{
    AS5600_SCL = 1;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ
    CY = AS5600_SDA;                   //��Ӧ���ź�
    AS5600_SCL = 0;                    //����ʱ����
    AS5600_Delay5us();                 //��ʱ

    return CY;
}

/**************************************
��IIC���߷���һ���ֽ�����
**************************************/
void AS5600_SendByte(uchar dat)
{
    uchar i;

    for (i=0; i<8; i++)         //8λ������
    {
        dat <<= 1;              //�Ƴ����ݵ����λ
        AS5600_SDA = CY;               //�����ݿ�
        AS5600_SCL = 1;                //����ʱ����
        AS5600_Delay5us();             //��ʱ
        AS5600_SCL = 0;                //����ʱ����
        AS5600_Delay5us();             //��ʱ
    }
    AS5600_RecvACK();
}

/**************************************
��IIC���߽���һ���ֽ�����
**************************************/
uchar AS5600_RecvByte()
{
    uchar i;
    uchar dat = 0;

    AS5600_SDA = 1;                    //ʹ���ڲ�����,׼����ȡ����,
    for (i=0; i<8; i++)         //8λ������
    {
        dat <<= 1;
        AS5600_SCL = 1;                //����ʱ����
        AS5600_Delay5us();             //��ʱ
        dat |= AS5600_SDA;             //������               
        AS5600_SCL = 0;                //����ʱ����
        AS5600_Delay5us();             //��ʱ
    }
    return dat;
}

//***************************************************

void Single_Write_AS5600(uchar REG_Address,uchar REG_data)
{
    AS5600_Start();                  //��ʼ�ź�
    AS5600_SendByte(AS5600_SlaveAddress);   //�����豸��ַ+д�ź�
    AS5600_SendByte(REG_Address);    //�ڲ��Ĵ�����ַ����ο�����pdf 
    AS5600_SendByte(REG_data);       //�ڲ��Ĵ������ݣ���ο�����pdf
    AS5600_Stop();                   //����ֹͣ�ź�
}

//********���ֽڶ�ȡ�ڲ��Ĵ���*************************
uchar Single_Read_AS5600(uchar REG_Address)
{  uchar REG_data;
    AS5600_Start();                          //��ʼ�ź�
    AS5600_SendByte(AS5600_SlaveAddress);           //�����豸��ַ+д�ź�
    AS5600_SendByte(REG_Address);                   //���ʹ洢��Ԫ��ַ����0��ʼ	
    AS5600_Start();                          //��ʼ�ź�
    AS5600_SendByte(AS5600_SlaveAddress+1);         //�����豸��ַ+���ź�
    REG_data=AS5600_RecvByte();              //�����Ĵ�������
	AS5600_SendACK(1);   
	AS5600_Stop();                           //ֹͣ�ź�
    return REG_data; 
}
//******************************************************
//
//��������AS5600�ڲ��Ƕ����ݣ���ַ��Χ0x3~0x5
//
//******************************************************
void Read_AS5600(void)
{  
    AS5600_Start();                          //��ʼ�ź�
    AS5600_SendByte(AS5600_SlaveAddress);           //�����豸��ַ+д�ź�
    AS5600_SendByte(0x03);                   //���ʹ洢��Ԫ��ַ����0x3��ʼ	
    AS5600_Start();                          //��ʼ�ź�
    AS5600_SendByte(AS5600_SlaveAddress+1);         //�����豸��ַ+���ź�
	
	AS5600_BUF[0] = AS5600_RecvByte();          //BUF[0]�洢����
	AS5600_SendACK(0);                //��ӦACK
	AS5600_BUF[1] = AS5600_RecvByte();          //BUF[0]�洢����
	AS5600_SendACK(1);                //���һ��������Ҫ��NOACK

    AS5600_Stop();                          //ֹͣ�ź�
 //   AS5600_Delay5ms();
}
