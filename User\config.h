#ifndef		__CONFIG_H
#define		__CONFIG_H

//========================================================================
//                               ��ʱ�Ӷ���
//========================================================================
#define SYS_VOL 5.0  	//ϵͳ������ѹ��V��
//��ѡ24000000L 27000000L 30000000L	33177600L 35000000L
#define MAIN_Fosc		24000000L	//������ʱ��
#define Main_Fosc2	(MAIN_Fosc / 2)  //��ʱ��/2
#define Main_Fosc4	(MAIN_Fosc / 4)  //��ʱ��/4
#define Main_Fosc8	(MAIN_Fosc / 8)  //��ʱ��/8
#define Main_Fosc16	(MAIN_Fosc / 16)  //��ʱ��/16
#define Main_Fosc_KHZ	(MAIN_Fosc / 1000)  //��ʱ��KHZ
#define Main_Fosc_MHZ	(MAIN_Fosc / 1000000)  //��ʱ��MHZ
//                             �������ͺ궨��
//========================================================================
#ifndef NULL
 #define NULL ((void *) 0)
#endif
 
typedef bit BOOL;
typedef char int8;
typedef int int16;
typedef long int32;

typedef unsigned char BYTE;
typedef unsigned int WORD;
typedef unsigned long DWORD;

typedef unsigned char u8;
typedef unsigned int u16;
typedef unsigned long u32;

typedef unsigned char uint8;
typedef unsigned int uint16;
typedef unsigned long uint32;

typedef unsigned char uchar;
typedef unsigned int uint;
typedef unsigned int ushort;
typedef unsigned long ulong;

typedef unsigned char uint8_t;
typedef unsigned int uint16_t;
typedef unsigned long uint32_t; 

#define max(A,B) ((A)>(B)?(A):(B))  //���ֵ
#define min(A,B) ((A)<(B)?(A):(B))  //��Сֵ

#define PI 3.14159  //Բ����

//========================================================================
//                                ͷ�ļ�
//========================================================================

#include "STC32G.h"
#include <stdlib.h>
#include <stdio.h>
#include <intrins.h>             //������ͷ�ļ�
#include <math.h>                //��ѧ���ͷ�ļ�
#include "string.h"
#include "isr.h"								 //��Ƭ���ж����
#include "GPIO.h"								 //GPI0��ز�������ͷ�ļ�
#include "PIT.h"
#include "Delay.h"
#include "INT.h"
#include "UART.h"
#include "IIC.h"
#include "PWM.h"
#include "ADC.h"
#include "usb.h"
#include "M_string.h"
#include "CAN.h"
#include "MKS.h"
#include "SPI.h"   //A1��е�ۿ��ƺ�����
#include "DMA.h"   //D2���̿��ƺ�����
#include "D2Car.h" //D2小车电机控制函数库
#include "EEPROM.h"
#include "OLED_SPI.h"
#include "MPU6050.h"

////========================================================================
////                            ���źͺ궨��
////========================================================================


sbit Reset_PIN = P3^2;
sbit KEY1 = P3^2;
sbit KEY2 = P3^4;

sbit LED = P4^5;
sbit BUZZ = P4^4;
sbit PWR_EN = P4^7;
sbit HZ = P3^7;
sbit TF_CD = P4^6;

sbit SW1 = P3^4;
sbit SW2 = P3^5;
sbit SW3 = P2^6;
sbit SW4 = P2^7;

#define OLED_ADDR 0X78
#define QMC5883L_ADDR 0X0C
#define MPU6050_ADDR 0XD2
#define DS3231_ADDR 0XD0
#define SPL06_ADDR 0X76		
#define HTU21D_ADDR 0X80


#define PWR_ADC_R1 94.16
#define PWR_ADC_R2 10.0
#define PWR_ADC_K ((PWR_ADC_R1+PWR_ADC_R2)/PWR_ADC_R2)

#define CAN_PORT 2  //CAN�˿�


////========================================================================
////                            �ⲿ�����ͱ�������
////========================================================================
extern void System_init(void);  //ϵͳ��ʼ��
extern void Usb_Rst(void);
extern void Key_Rst(void);
extern void set_clk(void);

float fmax(float a, float b);
	
extern uchar BCD2HEX(uchar val);
extern uchar HEX2BCD(uchar val);
extern long Limit_int(long min,long num,long max); //�����޷�����
extern float Limit_float(float min,float num,float max);//�����޷�����
extern float sq(float num);  //ƽ������

extern uint32 sys_clk;
extern uint MOTOR_state;
extern bit USB_flag;

extern union float_uchar;
extern union long_uchar;
extern union int_uchar;	
extern union char_uchar;	


#endif