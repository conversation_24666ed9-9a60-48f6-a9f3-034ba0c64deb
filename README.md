# EC11编码器控制舵机系统

## 功能概述
基于EC11旋转编码器控制PWM来精确控制双舵机运动的简洁系统，通过编码器的正转和反转实现舵机位置的精确调节，支持按键切换控制不同舵机。

## 主要功能

### 1. EC11编码器控制
- **旋转检测**: 通过INT1中断实时检测EC11编码器旋转
- **方向识别**: 自动识别编码器正转和反转方向
- **PWM调节**: 根据旋转方向增加或减少PWM值
- **实时响应**: 编码器旋转立即更新当前选中舵机位置
- **舵机切换**: 按下编码器按键切换控制的舵机(舵机1↔舵机2)

### 2. 双舵机精确控制
- **双舵机支持**: 同时支持两个舵机(P00和P01)的独立控制
- **PWM范围**: 500us-2500us的完整舵机控制范围
- **步长调节**: 每次旋转调节20us的PWM步长
- **边界保护**: 自动限制PWM值在有效范围内
- **平滑控制**: 连续旋转实现舵机位置的平滑调节
- **切换指示**: LED指示当前控制的舵机

### 3. 系统特点
- **简洁设计**: 仅118行代码实现双舵机控制功能
- **高效响应**: 中断驱动的实时控制
- **精确定位**: 20us步长提供精确的位置控制
- **稳定可靠**: 边界检查确保系统安全运行
- **智能切换**: 通过按下编码器按键智能切换控制舵机
- **状态指示**: LED指示当前控制的舵机状态

### 4. 工作模式
- **编码器控制**: 旋转编码器精确控制当前选中的舵机位置
- **舵机切换**: 按下编码器按键在舵机1和舵机2之间切换
- **双舵机独立**: 两个舵机可以独立设置不同的位置

## 硬件连接

| 功能 | 引脚 | 说明 |
|------|------|------|
| 舵机1 PWM | P0^0 | PWM输出控制舵机1位置 |
| 舵机2 PWM | P0^1 | PWM输出控制舵机2位置 |
| EC11编码器A相 | P3^2 | 编码器旋转信号输入(INT1中断) |
| EC11编码器B相 | P3^4 | 编码器方向检测信号 |
| EC11编码器按键 | P3^3 | 编码器按键输入，用于舵机切换 |
| 状态指示LED | P4^5 | LED指示当前控制的舵机 |

## 工作流程

### EC11编码器双舵机控制流程：
1. **系统初始化**: 两个舵机都初始化到中位位置(750对应1500us)
2. **舵机选择**: 默认控制舵机1(P00)，LED状态指示当前舵机
3. **编码器检测**: INT1中断实时检测编码器旋转
4. **PWM调节**:
   - 编码器正转: 当前舵机PWM值增加20us
   - 编码器反转: 当前舵机PWM值减少20us
   - 自动限制在500us-2500us范围内
5. **舵机响应**: PWM变化立即更新当前选中的舵机位置
6. **舵机切换**: 按下编码器按键在舵机1和舵机2之间切换
7. **连续控制**: 持续旋转实现当前舵机的精确位置调节

### 控制参数：
- **PWM范围**: 500us(最小) - 2500us(最大)
- **中位位置**: 1500us (对应PWM值750)
- **调节步长**: 20us/步
- **中断触发**: 下降沿触发INT1中断
- **方向检测**: P3^4引脚电平判断旋转方向
- **舵机切换**: P3^3引脚低电平触发舵机切换
- **状态指示**: LED状态变化指示舵机切换

## 参数配置

### 舵机控制参数
```c
#define SERVO_CLOSE_POS 1250  // 机械臂收缩位置
#define SERVO_OPEN_POS  250   // 机械臂松开位置
#define SERVO_NEUTRAL   750   // 舵机中位
```

### 控制逻辑
- **收缩条件**: IR_ERR > 0 (红外检测到目标)
- **松开条件**: IR_ERR < 0 (红外偏离目标)
- **中位条件**: IR_ERR = 0 (无偏差)
- **运动步长**: 5个单位/次 (平滑运动)
- **PWM频率**: 50Hz
- **更新周期**: 主循环实时更新

### 编码器控制原理
- **中断触发**: 编码器旋转触发INT1中断
- **方向检测**: 读取P3^4引脚电平判断旋转方向
- **PWM更新**: 根据方向增加或减少PWM值
- **控制逻辑**:
  - 正转(EC11_DIR=1): servo_pwm += PWM_STEP
  - 反转(EC11_DIR=0): servo_pwm -= PWM_STEP
  - 边界检查: 限制在SERVO_MIN_POS到SERVO_MAX_POS范围

## 核心算法

```c
void EC11_app(void) {
    if(KEY_dat[0]==0 && KEY_dat[1]!=0) { // 按键按下，复位到中位
        NUM=0;
        servo_pulse = SERVO_CENTER_PULSE;
        servo_control(servo_pulse);
    }
    KEY_dat[1] = KEY_dat[0];

    if(EC11_R_flag) {  // 编码器旋转
        if(EC11_DIR) {  // 正转
            NUM++;
            servo_pulse += 20; // 增加脉宽，舵机正转
        } else { // 反转
            NUM--;
            servo_pulse -= 20; // 减少脉宽，舵机反转
        }
        servo_control(servo_pulse); // 控制舵机
        EC11_R_flag=0;
    }
}

void servo_control(uint16 pulse) {
    pulse = Limit_int(SERVO_MIN_PULSE, pulse, SERVO_MAX_PULSE); // 限制脉宽范围
    PWM_change(PWMB_CH1_P01, pulse); // 更改PWM脉宽
}
```

## 使用说明

1. **系统启动**: 上电后两个舵机都自动初始化到中位位置(1500us)，默认控制舵机1
2. **舵机切换**:
   - 按下编码器按键 → 切换到舵机2 → LED状态改变
   - 再次按下编码器按键 → 切换回舵机1 → LED状态改变
3. **编码器控制**:
   - 顺时针旋转编码器 → 当前舵机PWM值增加 → 舵机正转
   - 逆时针旋转编码器 → 当前舵机PWM值减少 → 舵机反转
   - 连续旋转可实现精确的位置调节
4. **独立控制**: 两个舵机可以设置为不同的位置，互不影响
5. **位置范围**: PWM值自动限制在500us-2500us范围内
6. **实时响应**: 编码器每次旋转立即更新当前选中舵机的位置
## 注意事项

- 确保EC11编码器与P3^2(INT1)、P3^4、P3^3引脚正确连接
- 两个舵机分别连接到P0^0和P0^1引脚
- 舵机供电电压应稳定在4.8V-6V之间
- 编码器信号线应避免干扰，确保信号稳定
- 两个舵机运动范围内应无障碍物
- 系统响应速度取决于编码器旋转速度
- 按键切换功能需要编码器按键正确连接到P3^3
- LED指示灯连接到P4^5，用于指示当前控制的舵机

## 技术特点

### 代码简洁性
- **总代码量**: 仅118行代码实现双舵机控制功能
- **核心算法**: 编码器控制函数仅26行代码
- **按键处理**: 舵机切换函数仅18行代码
- **中断处理**: 简洁高效的中断服务函数
- **无冗余**: 移除所有不必要的功能模块
- **智能切换**: 集成按键舵机切换功能

### 实时性能
- **中断响应**: 编码器旋转立即触发中断
- **即时更新**: PWM值变化立即反映到当前选中舵机
- **按键检测**: 主循环连续检测按键状态，实现舵机切换
- **状态指示**: LED状态实时反映当前控制的舵机
- **高精度**: 20us步长提供精确控制

### 系统稳定性
- **边界保护**: 自动限制PWM值在有效范围
- **防抖动**: 中断标志位确保单次处理
- **按键去抖**: 按键状态缓存实现去抖功能，防止误触发
- **状态管理**: 舵机选择状态可靠切换
- **可靠性**: 简单的逻辑减少故障点
- **易维护**: 清晰的代码结构便于调试和扩展
